from fastapi import <PERSON><PERSON><PERSON>, Request, Form
from fastapi.staticfiles import Static<PERSON>iles
from fastapi.templating import Jin<PERSON>2Templates
from fastapi.responses import HTMLResponse, JSONResponse
from typing import Optional
import uvicorn
import os

app = FastAPI(title="Wedding Invitation Website")

# Create static and templates directories if they don't exist
os.makedirs("static", exist_ok=True)
os.makedirs("templates", exist_ok=True)

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")

# Templates
templates = Jinja2Templates(directory="templates")

@app.get("/", response_class=HTMLResponse)
async def read_root(request: Request):
    """Serve the main wedding invitation page"""
    return templates.TemplateResponse("index.html", {"request": request})

@app.post("/rsvp")
async def submit_rsvp(
    name: str = Form(...),
    email: str = Form(...),
    attending: str = Form(...),
    guests: int = Form(1),
    dietary_restrictions: Optional[str] = Form(None),
    message: Optional[str] = Form(None)
):
    """Handle RSVP form submissions"""
    rsvp_data = {
        "name": name,
        "email": email,
        "attending": attending,
        "guests": guests,
        "dietary_restrictions": dietary_restrictions,
        "message": message
    }
    
    # In a real application, you would save this to a database
    # For now, we'll just return a success response
    print(f"RSVP received: {rsvp_data}")
    
    return JSONResponse(content={
        "status": "success",
        "message": "Thank you for your RSVP! We're excited to celebrate with you."
    })

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy"}

if __name__ == "__main__":
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True)

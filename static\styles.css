/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #d4af37;
    --secondary-color: #8b7355;
    --accent-color: #f5f5dc;
    --text-dark: #2c2c2c;
    --text-light: #666;
    --white: #ffffff;
    --cream: #faf8f5;
    --gold-gradient: linear-gradient(135deg, #d4af37, #f4e4bc, #e6c866);
    --gold-gradient-hover: linear-gradient(135deg, #e6c866, #d4af37, #c9a332);
    --luxury-gradient: linear-gradient(135deg, #d4af37 0%, #f4e4bc 50%, #d4af37 100%);
    --shadow-light: 0 4px 20px rgba(212, 175, 55, 0.1);
    --shadow-medium: 0 8px 30px rgba(212, 175, 55, 0.15);
    --shadow-heavy: 0 15px 50px rgba(212, 175, 55, 0.2);
    --shadow-luxury: 0 20px 60px rgba(0,0,0,0.1), 0 8px 25px rgba(212, 175, 55, 0.2);
    --border-radius: 20px;
    --transition-smooth: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Noto Sans SC', 'Lato', sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    background-color: var(--cream);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Typography */
h1, h2, h3, h4 {
    font-family: 'Noto Serif SC', 'Playfair Display', serif;
    font-weight: 600;
}

h1 {
    font-size: 3.5rem;
    line-height: 1.2;
}

h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    z-index: 1000;
    transition: var(--transition-smooth);
    box-shadow: var(--shadow-light);
    border-bottom: 1px solid rgba(212, 175, 55, 0.1);
}

.navbar.scrolled {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: var(--shadow-medium);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 80px;
}

.nav-logo {
    font-family: 'Noto Serif SC', 'Playfair Display', serif;
    font-size: 1.8rem;
    font-weight: 700;
    background: var(--luxury-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
}

.nav-logo::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 2px;
    background: var(--luxury-gradient);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.nav-logo:hover::after {
    transform: scaleX(1);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: var(--text-dark);
    font-weight: 400;
    transition: color 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: var(--primary-color);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background: var(--text-dark);
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    height: 100vh;
    background: linear-gradient(135deg, rgba(212, 175, 55, 0.1), rgba(0,0,0,0.4)),
                url('/static/images/hero-bg.jpg');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, transparent 0%, rgba(0,0,0,0.3) 100%);
    z-index: 1;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(212, 175, 55, 0.1), rgba(0,0,0,0.2));
    z-index: 1;
}

.hero-content {
    position: relative;
    z-index: 3;
    color: var(--white);
    animation: fadeInUp 1.2s ease-out;
    text-shadow: 0 4px 20px rgba(0,0,0,0.5);
}

.couple-names {
    font-size: 4.5rem;
    margin-bottom: 1.5rem;
    text-shadow: 0 4px 20px rgba(0,0,0,0.5);
    background: linear-gradient(135deg, #ffffff, #f4e4bc);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
    animation: glow 3s ease-in-out infinite alternate;
}

@keyframes glow {
    from {
        text-shadow: 0 4px 20px rgba(0,0,0,0.5), 0 0 30px rgba(212, 175, 55, 0.3);
    }
    to {
        text-shadow: 0 4px 20px rgba(0,0,0,0.5), 0 0 40px rgba(212, 175, 55, 0.5);
    }
}

.wedding-tagline {
    font-size: 1.8rem;
    font-style: italic;
    margin-bottom: 2.5rem;
    opacity: 0.95;
    font-weight: 300;
    letter-spacing: 1px;
}

.wedding-date {
    font-size: 1.3rem;
    margin-bottom: 3.5rem;
    line-height: 2;
    font-weight: 300;
}

.cta-button {
    display: inline-block;
    padding: 18px 45px;
    background: var(--luxury-gradient);
    color: var(--text-dark);
    text-decoration: none;
    border-radius: 50px;
    font-weight: 600;
    transition: var(--transition-smooth);
    box-shadow: var(--shadow-luxury);
    text-transform: uppercase;
    letter-spacing: 2px;
    position: relative;
    overflow: hidden;
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.6s ease;
}

.cta-button:hover::before {
    left: 100%;
}

.cta-button:hover {
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 25px 70px rgba(0,0,0,0.2), 0 10px 30px rgba(212, 175, 55, 0.4);
    background: var(--gold-gradient-hover);
}

.scroll-indicator {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    color: var(--white);
    font-size: 1.5rem;
    animation: bounce 2s infinite;
}

/* Section Styling */
section {
    padding: 100px 0;
}

.section-header {
    text-align: center;
    margin-bottom: 60px;
}

.section-header h2 {
    color: var(--text-dark);
    margin-bottom: 20px;
}

.divider {
    width: 80px;
    height: 3px;
    background: var(--gold-gradient);
    margin: 0 auto;
    border-radius: 2px;
}

/* Couple Section */
.couple-section {
    background: var(--white);
}

.couple-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.story-text {
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 1.5rem;
    color: var(--text-light);
}

.photo-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.photo-item {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: var(--shadow-medium);
    transition: transform 0.3s ease;
}

.photo-item:hover {
    transform: translateY(-5px);
}

.couple-img {
    width: 100%;
    height: 280px;
    object-fit: cover;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    border-radius: 15px;
    filter: brightness(1) contrast(1.05) saturate(1.1);
}

.couple-img:hover {
    transform: scale(1.08);
    filter: brightness(1.1) contrast(1.1) saturate(1.2);
    box-shadow: 0 15px 40px rgba(0,0,0,0.3);
}

/* 图片加载状态 */
.couple-img, .gallery-img {
    background: linear-gradient(45deg, #f0f0f0, #e0e0e0);
    background-size: 400% 400%;
    animation: shimmer 1.5s ease-in-out infinite;
}

.couple-img[src], .gallery-img[src] {
    animation: none;
    background: none;
}

@keyframes shimmer {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* Details Section */
.details-section {
    background: var(--cream);
}

.details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
}

.detail-card {
    background: var(--white);
    padding: 40px 30px;
    border-radius: 15px;
    text-align: center;
    box-shadow: var(--shadow-light);
    transition: all 0.3s ease;
}

.detail-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.detail-icon {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 20px;
}

.detail-card h3 {
    color: var(--text-dark);
    margin-bottom: 15px;
}

.detail-card p {
    color: var(--text-light);
    line-height: 1.6;
}

/* Timeline Section */
.timeline-section {
    background: var(--white);
}

.timeline {
    max-width: 800px;
    margin: 0 auto;
    position: relative;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 0;
    width: 3px;
    background: var(--gold-gradient);
    transform: translateX(-50%);
}

.timeline-item {
    display: flex;
    margin-bottom: 50px;
    position: relative;
}

.timeline-item:nth-child(odd) {
    flex-direction: row-reverse;
}

.timeline-time {
    flex: 0 0 120px;
    background: var(--gold-gradient);
    color: var(--text-dark);
    padding: 10px 20px;
    border-radius: 25px;
    text-align: center;
    font-weight: 600;
    margin: 0 30px;
    box-shadow: var(--shadow-light);
}

.timeline-content {
    flex: 1;
    background: var(--cream);
    padding: 25px 30px;
    border-radius: 15px;
    box-shadow: var(--shadow-light);
}

.timeline-content h4 {
    color: var(--text-dark);
    margin-bottom: 10px;
}

.timeline-content p {
    color: var(--text-light);
}

/* Gallery Section */
.gallery-section {
    background: var(--cream);
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
}

.gallery-item {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: var(--shadow-medium);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    cursor: pointer;
    background: var(--white);
}

.gallery-item:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 20px 60px rgba(0,0,0,0.25);
}

.gallery-img {
    width: 100%;
    height: 280px;
    object-fit: cover;
    transition: all 0.4s ease;
    filter: brightness(1) saturate(1);
}

.gallery-item:hover .gallery-img {
    transform: scale(1.1);
    filter: brightness(1.1) saturate(1.2);
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(212, 175, 55, 0.8), rgba(139, 115, 85, 0.8));
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.4s ease;
    backdrop-filter: blur(2px);
}

.gallery-item:hover .gallery-overlay {
    opacity: 1;
}

.gallery-text {
    color: var(--white);
    font-size: 1.2rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 2px;
    transform: translateY(20px);
    transition: transform 0.4s ease;
}

.gallery-item:hover .gallery-text {
    transform: translateY(0);
}

/* 高级图片查看器样式 */
.lightbox-overlay {
    backdrop-filter: blur(10px);
    animation: lightboxFadeIn 0.3s ease-out;
}

.lightbox-content {
    animation: lightboxZoomIn 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.lightbox-img {
    box-shadow: 0 20px 60px rgba(0,0,0,0.5);
    border-radius: 10px;
}

.lightbox-close {
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
}

.lightbox-close:hover {
    background: rgba(255,255,255,0.3) !important;
    transform: scale(1.1);
}

@keyframes lightboxFadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes lightboxZoomIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* 图片错误处理 */
.couple-img, .gallery-img {
    position: relative;
}

.couple-img::after, .gallery-img::after {
    content: '图片加载中...';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #999;
    font-size: 0.9rem;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.couple-img[src=""], .gallery-img[src=""],
.couple-img:not([src]), .gallery-img:not([src]) {
    background: #f5f5f5;
}

.couple-img[src=""]:after, .gallery-img[src=""]:after,
.couple-img:not([src]):after, .gallery-img:not([src]):after {
    opacity: 1;
}

/* RSVP Section */
.rsvp-section {
    background: var(--white);
}

.rsvp-subtitle {
    color: var(--text-light);
    font-style: italic;
    margin-top: 10px;
}

.rsvp-form-container {
    max-width: 600px;
    margin: 0 auto;
}

.rsvp-form {
    background: var(--cream);
    padding: 40px;
    border-radius: 15px;
    box-shadow: var(--shadow-light);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--text-dark);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
    background: var(--white);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
}

.submit-btn {
    width: 100%;
    padding: 15px;
    background: var(--gold-gradient);
    color: var(--text-dark);
    border: none;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

/* Footer */
.footer {
    background: var(--text-dark);
    color: var(--white);
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 40px;
    align-items: center;
    margin-bottom: 40px;
}

.contact-info h3 {
    color: var(--primary-color);
    margin-bottom: 20px;
}

.contact-info p {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.footer-divider {
    width: 2px;
    height: 100px;
    background: var(--primary-color);
}

.footer-message {
    text-align: right;
}

.footer-message p {
    font-size: 1.2rem;
    margin-bottom: 20px;
    font-style: italic;
}

.social-links {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
}

.social-link {
    display: inline-block;
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    color: var(--text-dark);
    text-align: center;
    line-height: 40px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.social-link:hover {
    transform: translateY(-3px);
    background: var(--white);
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #444;
    color: #999;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateX(-50%) translateY(0);
    }
    40% {
        transform: translateX(-50%) translateY(-10px);
    }
    60% {
        transform: translateX(-50%) translateY(-5px);
    }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .container {
        padding: 0 30px;
    }

    .gallery-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 25px;
    }

    .details-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 30px;
    }
}

@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }

    .nav-container {
        height: 70px;
        padding: 0 20px;
    }

    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background: rgba(255, 255, 255, 0.98);
        backdrop-filter: blur(15px);
        width: 100%;
        text-align: center;
        transition: var(--transition-smooth);
        box-shadow: var(--shadow-luxury);
        padding: 30px 0;
        gap: 1rem;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-link {
        font-size: 1.1rem;
        padding: 10px 0;
    }

    .couple-names {
        font-size: 3rem;
        margin-bottom: 1rem;
    }

    .wedding-tagline {
        font-size: 1.4rem;
        margin-bottom: 2rem;
    }

    .wedding-date {
        font-size: 1.1rem;
        margin-bottom: 2.5rem;
    }

    .cta-button {
        padding: 15px 35px;
        font-size: 0.9rem;
    }

    .couple-content {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }

    .photo-grid {
        justify-items: center;
    }

    .details-grid {
        grid-template-columns: 1fr;
        gap: 25px;
    }

    .detail-card {
        padding: 30px 25px;
    }

    .timeline::before {
        left: 25px;
    }

    .timeline-item {
        flex-direction: column;
        padding-left: 60px;
        margin-bottom: 40px;
    }

    .timeline-item:nth-child(odd) {
        flex-direction: column;
    }

    .timeline-time {
        margin: 0 0 15px 0;
        align-self: flex-start;
        flex: none;
        width: auto;
    }

    .gallery-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .gallery-item {
        max-width: 400px;
        margin: 0 auto;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .rsvp-form {
        padding: 30px 25px;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 30px;
    }

    .footer-message {
        text-align: center;
    }

    .social-links {
        justify-content: center;
    }

    .footer-divider {
        width: 100px;
        height: 2px;
        margin: 0 auto;
    }

    section {
        padding: 80px 0;
    }

    .section-header {
        margin-bottom: 50px;
    }

    h2 {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 20px;
    }

    .couple-names {
        font-size: 2.2rem;
        line-height: 1.1;
    }

    .wedding-tagline {
        font-size: 1.2rem;
    }

    .wedding-date {
        font-size: 1rem;
    }

    .cta-button {
        padding: 12px 30px;
        font-size: 0.8rem;
        letter-spacing: 1px;
    }

    .detail-card {
        padding: 25px 20px;
    }

    .detail-icon {
        font-size: 2.5rem;
    }

    .timeline-time {
        font-size: 0.9rem;
        padding: 8px 15px;
    }

    .timeline-content {
        padding: 20px 25px;
    }

    .gallery-img {
        height: 220px;
    }

    .rsvp-form {
        padding: 25px 20px;
    }

    .submit-btn {
        padding: 12px;
        font-size: 1rem;
    }

    section {
        padding: 60px 0;
    }

    h2 {
        font-size: 1.8rem;
    }

    h3 {
        font-size: 1.3rem;
    }
}
